@theme {
    /* <PERSON><PERSON> Theme Colors */

    --color-primary: var(--color-mojo-brown-1);  /*AF9A80*/
    --color-primary-light: var(--color-mojo-brown-2);
    --color-primary-lighter: var(--color-mojo-brown-3);
    --color-primary-dark: var(--color-mojo-brown-4);
    --color-primary-darker: var(--color-mojo-bronw-6);
    --color-secondary: var(--color-mojo-cream-1);
    --color-secondary-light: var(--color-mojo-cream);
    --color-secondary-dark: var(--color-mojo-cream-2);
    --color-brown: var(--color-mojo-brown-1);
    --color-brown-light: var(--color-mojo-brown-2);
    --color-brown-lighter: var(--color-mojo-brown-3);
    --color-brown-dark: var(--color-mojo-brown-5);
    --color-cream: var(--color-mojo-cream-4);
    --color-cream-light: var(--color-mojo-cream-3);
    --color-primary-bg-main: var(--color-background-mojo);
    --color-primary-bg-card: var(--color-background-card);
    --color-primary-border-card: var(--color-card-border);

  /* Mojo Muse Theme Colors with state variants */
  /* Primary color variants */
  --color-primary-bg-fill-rest: var(--color-mojo-brown-1);
  --color-primary-bg-fill-hover: var(--color-mojo-brown-4);
  --color-primary-bg-fill-active: var(--color-mojo-brown-5);
  --color-primary-bg-surface-rest: var(--color-mojo-brown-3);
  --color-primary-bg-surface-hover: var(--color-mojo-brown-2);
  --color-primary-bg-surface-active: var(--color-mojo-brown-1);
  --color-primary-text-rest: var(--color-mojo-brown-1);
  --color-primary-text-on-bg-fill: var(--color-mojo-cream-1);
  --color-primary-icon-rest: var(--color-mojo-brown-1);
  --color-primary-icon-on-bg-fill: var(--color-mojo-cream-1);
  --color-primary-stroke-1-rest: var(--color-mojo-brown-1);
  --color-primary-stroke-2-rest: var(--color-mojo-brown-2);

  /* Secondary color variants */
  --color-secondary-bg-fill-rest: var(--color-mojo-cream-1);
  --color-secondary-bg-fill-hover: var(--color-mojo-cream-2);
  --color-secondary-bg-fill-active: var(--color-mojo-cream-3);
  --color-secondary-bg-surface-rest: var(--color-mojo-cream-1);
  --color-secondary-bg-surface-hover: var(--color-mojo-cream-2);
  --color-secondary-bg-surface-active: var(--color-mojo-cream-3);
  --color-secondary-text-rest: var(--color-mojo-cream-1);
  --color-secondary-text-on-bg-fill: var(--color-mojo-brown-1);
  --color-secondary-icon-rest: var(--color-mojo-cream-1);
  --color-secondary-icon-on-bg-fill: var(--color-mojo-brown-1);
  --color-secondary-stroke-1-rest: var(--color-mojo-cream-1);
  --color-secondary-stroke-2-rest: var(--color-mojo-cream-2);

  /* Brown color variants */
  --color-brown-bg-fill-rest: var(--color-mojo-brown-1);
  --color-brown-bg-fill-hover: var(--color-mojo-brown-4);
  --color-brown-bg-fill-active: var(--color-mojo-brown-5);
  --color-brown-bg-surface-rest: var(--color-mojo-brown-3);
  --color-brown-bg-surface-hover: var(--color-mojo-brown-2);
  --color-brown-bg-surface-active: var(--color-mojo-brown-1);
  --color-brown-text-rest: var(--color-mojo-brown-1);
  --color-brown-text-on-bg-fill: var(--color-mojo-cream-1);
  --color-brown-icon-rest: var(--color-mojo-brown-1);
  --color-brown-icon-on-bg-fill: var(--color-mojo-cream-1);
  --color-brown-stroke-1-rest: var(--color-mojo-brown-1);
  --color-brown-stroke-2-rest: var(--color-mojo-brown-2);

  /* Cream color variants */
  --color-cream-bg-fill-rest: var(--color-mojo-cream-1);
  --color-cream-bg-fill-hover: var(--color-mojo-cream-2);
  --color-cream-bg-fill-active: var(--color-mojo-cream-3);
  --color-cream-bg-surface-rest: var(--color-mojo-cream-1);
  --color-cream-bg-surface-hover: var(--color-mojo-cream-2);
  --color-cream-bg-surface-active: var(--color-mojo-cream-3);
  --color-cream-text-rest: var(--color-mojo-cream-1);
  --color-cream-text-on-bg-fill: var(--color-mojo-brown-1);
  --color-cream-icon-rest: var(--color-mojo-cream-1);
  --color-cream-icon-on-bg-fill: var(--color-mojo-brown-1);
  --color-cream-stroke-1-rest: var(--color-mojo-cream-1);
  --color-cream-stroke-2-rest: var(--color-mojo-cream-2);

  /*button color variants*/
  --color-cancel-button: var(--color-cancel);

  --color-brand-primary-bg-fill-rest: var(--color-brand-primary---blue-50);
  --color-brand-primary-bg-fill-hover: var(--color-brand-primary---blue-60);
  --color-status-positive-bg-fill-rest: var(--color-lime-12);
  --color-status-positive-bg-fill-hover: var(--color-lime-13);
  --color-brand-primary-text-rest: var(--color-brand-primary---blue-50);
  --color-brand-primary-text-on-bg-fill: var(--color-brand-primary---blue-5);
  --color-brand-primary-bg-surface-rest: var(--color-brand-primary---blue-5);
  --color-brand-primary-bg-surface-hover: var(--color-brand-primary---blue-10);
  --color-status-positive-bg-surface-rest: var(--color-lime-2);
  --color-status-positive-bg-surface-hover: var(--color-lime-3);
  --color-status-positive-bg-surface-active: var(--color-lime-4);
  --color-status-positive-text-rest: var(--color-lime-12);
  --color-status-positive-text-on-bg-fill: var(--color-lime-1);
  --color-status-negative-bg-surface-rest: var(--color-red-4);
  --color-status-negative-text-rest: var(--color-red-12);
  --color-status-negative-text-on-bg-fill: var(--color-red-1);
  --color-status-negative-bg-surface-hover: var(--color-red-5);
  --color-status-negative-bg-surface-active: var(--color-red-6);
  --color-status-negative-bg-fill-rest: var(--color-red-12);
  --color-status-negative-bg-fill-hover: var(--color-red-13);
  --color-status-warning-bg-surface-rest: var(--color-orange-3);
  --color-status-warning-text-rest: var(--color-orange-11);
  --color-status-warning-text-on-bg-fill: var(--color-orange-16);
  --color-status-warning-bg-surface-hover: var(--color-orange-4);
  --color-status-warning-bg-surface-active: var(--color-orange-5);
  --color-status-warning-bg-fill-rest: var(--color-orange-10);
  --color-status-warning-bg-fill-hover: var(--color-orange-11);
  --color-status-info-bg-surface-rest: var(--color-azure-3);
  --color-status-info-text-rest: var(--color-azure-11);
  --color-status-info-text-on-bg-fill: var(--color-azure-16);
  --color-status-info-bg-surface-hover: var(--color-azure-4);
  --color-status-info-bg-surface-active: var(--color-azure-5);
  --color-status-info-bg-fill-rest: var(--color-azure-9);
  --color-status-info-bg-fill-hover: var(--color-azure-10);
  --color-brand-primary-icon-rest: var(--color-brand-primary---blue-50);
  --color-brand-primary-icon-on-bg-fill: var(--color-brand-primary---blue-5);
  --color-brand-primary-stroke-1-rest: var(--color-neutral-9);
  --color-brand-primary-stroke-2-rest: var(--color-brand-primary---blue-50);
  --color-status-info-icon-rest: var(--color-azure-11);
  --color-status-info-icon-on-bg-fill: var(--color-azure-16);
  --color-status-info-stroke-1-rest: var(--color-azure-11);
  --color-status-info-stroke-2-rest: var(--color-azure-8);
  --color-status-positive-icon-rest: var(--color-lime-12);
  --color-status-positive-icon-on-bg-fill: var(--color-lime-1);
  --color-status-positive-stroke-1-rest: var(--color-lime-12);
  --color-status-positive-stroke-2-rest: var(--color-lime-5);
  --color-status-negative-icon-rest: var(--color-red-12);
  --color-status-negative-icon-on-bg-fill: var(--color-red-1);
  --color-status-negative-stroke-1-rest: var(--color-red-12);
  --color-status-negative-stroke-2-rest: var(--color-red-8);
  --color-status-warning-icon-rest: var(--color-orange-11);
  --color-status-warning-icon-on-bg-fill: var(--color-orange-16);
  --color-status-warning-stroke-primary-rest: var(--color-orange-11);
  --color-status-warning-stroke-secondary-rest: var(--color-orange-8);
  --color-neutral-bg-surface-secondary-hover: var(--color-neutral-6);
  --color-neutral-bg-surface-disabled: var(--color-alpha-black-5);
  --color-neutral-bg-surface-primary-hover: var(--color-neutral-4);
  --color-neutral-bg-surface-primary-rest: var(--color-neutral-1);
  --color-neutral-bg-surface-inverse: var(--color-neutral-15);
  --color-neutral-bg-surface-secondary-rest: var(--color-neutral-4);
  --color-neutral-bg-surface-tertiary-rest: var(--color-neutral-5);
  --color-neutral-bg-surface-tertiary-hover: var(--color-neutral-7);
  --color-neutral-text-primary-rest: var(--color-neutral-15);
  --color-neutral-text-secondary-rest: var(--color-neutral-13);
  --color-neutral-text-disabled: var(--color-neutral-11);
  --color-neutral-text-inverse: var(--color-neutral-1);
  --color-neutral-icon-primary-rest: var(--color-neutral-14);
  --color-neutral-icon-disabled: var(--color-neutral-10);
  --color-neutral-icon-inverse: var(--color-neutral-1);
  --color-neutral-icon-secondary-rest: var(--color-neutral-13);
  --color-neutral-stroke-primary-rest: var(--color-neutral-8);
  --color-neutral-stroke-secondary-rest: var(--color-neutral-7);
  --color-neutral-stroke-disabled: var(--color-neutral-7);
  --color-neutral-stroke-tertiary-rest: var(--color-neutral-10);
  --color-neutral-stroke-inverse: var(--color-neutral-13);
  --color-neutral-text-inverse-secondary: var(--color-neutral-11);
  --color-brand-primary-bg-surface-active: var(--color-brand-primary---blue-15);
  --color-neutral-icon-inverse-secondary: var(--color-neutral-11);
  --color-brand-primary-bg-fill-active: var(--color-brand-primary---blue-60);
  --color-status-info-bg-fill-active: var(--color-azure-11);
  --color-status-positive-bg-fill-active: var(--color-lime-14);
  --color-status-negative-bg-fill-active: var(--color-red-14);
  --color-status-warning-bg-fill-active: var(--color-orange-12);
  --color-neutral-text-tertiary-rest: var(--color-neutral-12);
  --color-neutral-bg-surface-primary-active: var(--color-neutral-5);
  --color-neutral-bg-surface-secondary-active: var(--color-neutral-7);
  --color-neutral-bg-surface-tertiary-active: var(--color-neutral-8);
  --color-brand-primary-stroke-3-rest: var(--color-brand-primary---blue-15);
  --color-neutral-icon-tertiary-rest: var(--color-neutral-11);
  --color-components-overlay-bg: var(--color-alpha-black-50);
  --color-extend-purple-bg-surface-rest: var(--color-purple-3);
  --color-extend-purple-icon-rest: var(--color-purple-12);
  --color-extend-purple-icon-on-bg-fill: var(--color-purple-1);
  --color-extend-purple-stroke-1-rest: var(--color-purple-12);
  --color-extend-purple-stroke-2-rest: var(--color-purple-8);
  --color-extend-purple-bg-surface-hover: var(--color-purple-4);
  --color-extend-purple-bg-surface-active: var(--color-purple-5);
  --color-extend-purple-bg-fill-rest: var(--color-purple-12);
  --color-extend-purple-bg-fill-hover: var(--color-purple-13);
  --color-extend-purple-bg-fill-active: var(--color-purple-14);
  --color-extend-purple-text-rest: var(--color-purple-12);
  --color-extend-purple-text-on-bg-fill: var(--color-purple-1);
  --color-extend-cyan-bg-surface-rest: var(--color-cyan-2);
  --color-extend-cyan-bg-surface-hover: var(--color-cyan-3);
  --color-extend-cyan-bg-surface-active: var(--color-cyan-4);
  --color-extend-cyan-bg-fill-rest: var(--color-cyan-11);
  --color-extend-cyan-bg-fill-hover: var(--color-cyan-12);
  --color-extend-cyan-bg-fill-active: var(--color-cyan-13);
  --color-extend-cyan-icon-rest: var(--color-cyan-11);
  --color-extend-cyan-icon-on-bg-fill: var(--color-cyan-1);
  --color-extend-cyan-stroke-1-rest: var(--color-cyan-11);
  --color-extend-cyan-stroke-2-rest: var(--color-cyan-7);
  --color-extend-cyan-text-rest: var(--color-cyan-11);
  --color-extend-cyan-text-on-bg-fill: var(--color-cyan-1);
  --color-neutral-stroke-white: var(--color-white);
  --color-neutral-stroke-black: var(--color-black);
  --color-extend-rose-bg-surface-rest: var(--color-rose-2);
  --color-extend-rose-bg-surface-hover: var(--color-rose-3);
  --color-extend-rose-bg-surface-active: var(--color-rose-4);
  --color-extend-rose-bg-fill-rest: var(--color-rose-12);
  --color-extend-rose-bg-fill-hover: var(--color-rose-13);
  --color-extend-rose-bg-fill-active: var(--color-rose-14);
  --color-extend-rose-icon-rest: var(--color-rose-12);
  --color-extend-rose-icon-on-bg-fill: var(--color-rose-1);
  --color-extend-rose-stroke-1-rest: var(--color-rose-12);
  --color-extend-rose-stroke-2-rest: var(--color-rose-8);
  --color-extend-rose-text-rest: var(--color-rose-12);
  --color-extend-rose-text-on-bg-fill: var(--color-rose-1);
  --color-brand-primary-orange-bg-surface-rest: var(
    --color-brand-primary---orange-5
  );
  --color-brand-primary-orange-bg-surface-hover: var(
    --color-brand-primary---orange-10
  );
  --color-brand-primary-orange-bg-surface-active: var(
    --color-brand-primary---orange-15
  );
  --color-brand-primary-orange-bg-fill-hover: var(
    --color-brand-primary---orange-60
  );
  --color-brand-primary-orange-bg-fill-active: var(
    --color-brand-primary---orange-70
  );
  --color-brand-primary-orange-icon-on-bg-fill: var(
    --color-brand-primary---orange-5
  );
  --color-brand-primary-orange-stroke-1-rest: var(--color-neutral-9);
  --color-brand-primary-orange-stroke-3-rest: var(
    --color-brand-primary---orange-15
  );
  --color-brand-primary-orange-text-on-bg-fill: var(
    --color-brand-primary---orange-5
  );
  --color-components-floating_button-bg-rest: var(--color-neutral-15);
  --color-components-floating_button-stroke-rest: var(--color-alpha-white-20);
  --color-components-floating_button-stroke-hover: var(--color-alpha-white-60);
  --color-components-floating_button-bg-hover: var(--color-neutral-14);
  --color-components-sidebar-menu_button-bg-rest: var(--color-alpha-white-20);
  --color-brand-primary-icon-hover: var(--color-brand-primary---blue-60);
  --color-neutral-text-inverse-disbled: var(--color-neutral-12);
  --color-neutral-icon-inverse-disabled: var(--color-neutral-12);
  --color-components-floating_button-stroke-disabled: var(
    --color-alpha-white-10
  );
  --color-neutral-icon-disabled-subtle: var(--color-neutral-8);
  --color-status-negative-stroke-1-hover: var(--color-red-13);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer components {
  .caption-regular {
    font-size: var(--font-size-xs);
    font-weight: 400;
    line-height: var(--line-height-xs);
  }

  .caption-bold {
    font-size: var(--font-size-xs);
    font-weight: 700;
    line-height: var(--line-height-xs);
  }

  .body-2-regular {
    font-size: var(--font-size-sm);
    font-weight: 400;
    line-height: var(--line-height-sm);
  }

  .body-1-regular {
    font-size: var(--font-size-md);
    font-weight: 400;
    line-height: var(--line-height-md);
  }

  .heading-3-bold {
    font-size: var(--font-size-sm);
    font-weight: 700;
    line-height: var(--line-height-sm);
  }

  .heading-2-bold {
    font-size: var(--font-size-md);
    font-weight: 400;
    line-height: var(--line-height-md);
  }

  .heading-1-bold {
    font-size: var(--font-size-lg);
    font-weight: 700;
    line-height: var(--line-height-lg);
  }

  .title-4-bold {
    font-size: var(--font-size-xl);
    font-weight: 700;
    line-height: var(--line-height-xl);
  }

  .title-3-bold {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    line-height: var(--line-height-2xl);
  }

  .title-2-bold {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    line-height: var(--line-height-3xl);
  }

  .title-1-bold {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    line-height: var(--line-height-4xl);
  }

  .display-1-extra-bold {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: var(--line-height-5xl);
  }
}

@layer utilities {
  .radius-none {
    border-radius: var(--border-radius-none);
  }

  .radius-050 {
    border-radius: var(--border-radius-050);
  }

  .radius-100 {
    border-radius: var(--border-radius-100);
  }

  .radius-150 {
    border-radius: var(--border-radius-150);
  }

  .radius-200 {
    border-radius: var(--border-radius-200);
  }

  .radius-300 {
    border-radius: var(--border-radius-300);
  }

  .radius-400 {
    border-radius: var(--border-radius-400);
  }

  .radius-500 {
    border-radius: var(--border-radius-500);
  }

  .radius-750 {
    border-radius: var(--border-radius-750);
  }

  .radius-circular {
    border-radius: var(--border-radius-circular);
  }

  /* icons size */
  .icon-12 {
    width: var(--icon-size-012);
    height: var(--icon-size-012);
  }

  .icon-16 {
    width: var(--icon-size-016);
    height: var(--icon-size-016);
  }

  .icon-20 {
    width: var(--icon-size-020);
    height: var(--icon-size-020);
  }

  .icon-24 {
    width: var(--icon-size-024);
    height: var(--icon-size-024);
  }
}
