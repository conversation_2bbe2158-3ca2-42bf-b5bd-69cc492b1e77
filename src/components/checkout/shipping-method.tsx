import React from "react";
import { Controller, type Control, type FieldErrors } from "react-hook-form";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@base/card";
import { RadioGroup, RadioGroupItem } from "@base/radio-group";
import { Label } from "@base/label";
import { SHIPPING_METHODS } from "@/pages/checkout/constants";
import type { CheckoutFormData } from "@/pages/checkout/constants";

interface ShippingMethodProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export const ShippingMethod: React.FC<ShippingMethodProps> = ({
  control,
  errors,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Shipping Method</CardTitle>
      </CardHeader>
      <CardContent>
        <Controller
          name="shippingMethod"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value}
              onValueChange={(value) => {
                field.onChange(value);
              }}
            >
              {SHIPPING_METHODS.map((method) => (
                <Card
                  key={method.id}
                  className={`cursor-pointer transition-colors ${
                    field.value === method.id
                      ? "border-primary bg-primary/5"
                      : ""
                  }`}
                  onClick={() => {
                    field.onChange(method.id);
                  }}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value={method.id} id={method.id} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <Label
                            htmlFor={method.id}
                            className="cursor-pointer font-medium"
                          >
                            {method.name}
                          </Label>
                          <span className="font-medium">
                            ฿{method.price.toLocaleString()}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                          {method.estimatedDays}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </RadioGroup>
          )}
        />
        {errors.shippingMethod && (
          <p className="mt-2 text-xs text-red-600">
            {errors.shippingMethod.message}
          </p>
        )}
      </CardContent>
    </Card>
  );
};
