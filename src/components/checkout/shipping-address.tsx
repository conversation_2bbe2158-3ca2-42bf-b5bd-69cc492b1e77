import React, { useState } from "react";
import {
  Controller,
  type Control,
  type FieldErrors,
  type UseFormSetValue,
} from "react-hook-form";
import {
  RadioGroup,
  RadioGroupItem,
} from "@/components/commons/base/radio-group";
import { Label } from "@/components/commons/base/label";
import { useAddresses } from "@/hooks/useAddresses";
import { AddressForm } from "@/components/address/address-form";
import type { CheckoutFormData } from "@/pages/checkout/constants";
import type { Address, AddressFormData } from "@/types/address";
import { AddAddressButton } from "@components/address";

interface ShippingAddressProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  setValue: UseFormSetValue<CheckoutFormData>;
}

export const ShippingAddress: React.FC<ShippingAddressProps> = ({
  control,
  setValue,
}) => {
  const { addresses, isLoading, addAddress } = useAddresses();
  const [selectedAddressId, setSelectedAddressId] = useState<string>("new");
  const [isFormOpen, setIsFormOpen] = useState(false);

  const mapAddressToCheckoutForm = (address: Address) => {
    const nameParts = address.recipient_name.split(" ");
    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    setValue("shippingAddress.firstName", firstName);
    setValue("shippingAddress.lastName", lastName);
    setValue("shippingAddress.company", "");
    setValue("shippingAddress.address_line1", address.address_line1);
    setValue("shippingAddress.address_line2", address.address_line2 || "");
    setValue("shippingAddress.subdistrict", address.subdistrict);
    setValue("shippingAddress.district", address.district);
    setValue("shippingAddress.city", address.city);
    setValue("shippingAddress.province", address.province);
    setValue("shippingAddress.postal_code", address.postal_code);
    setValue("shippingAddress.country", address.country);
  };

  const handleAddressSelect = (addressId: string) => {
    setSelectedAddressId(addressId);

    if (addressId !== "new") {
      const selectedAddress = addresses.find(
        (addr) => addr.id.toString() === addressId,
      );
      if (selectedAddress) {
        mapAddressToCheckoutForm(selectedAddress);
      }
    } else {
      // Clear form when "new" is selected
      setValue("shippingAddress.firstName", "");
      setValue("shippingAddress.lastName", "");
      setValue("shippingAddress.company", "");
      setValue("shippingAddress.address_line1", "");
      setValue("shippingAddress.address_line2", "");
      setValue("shippingAddress.subdistrict", "");
      setValue("shippingAddress.district", "");
      setValue("shippingAddress.city", "");
      setValue("shippingAddress.province", "");
      setValue("shippingAddress.postal_code", "");
      setValue("shippingAddress.country", "Thailand");
    }
  };

  const handleOpenAddressForm = () => {
    setIsFormOpen(true);
  };

  const handleAddAddress = async (data: AddressFormData) => {
    try {
      await addAddress(data);
      setIsFormOpen(false);
    } catch (error) {
      console.error("Error adding address:", error);
      throw error;
    }
  };

  return (
    <>
      <h2 className="text-[23px] font-medium text-black">ที่อยู่จัดส่ง</h2>

      {!isLoading && addresses.length > 0 && (
        <div className="space-y-3">
          <Controller
            name="shippingAddress"
            control={control}
            render={() => (
              <RadioGroup
                value={selectedAddressId}
                onValueChange={handleAddressSelect}
                className="space-y-3"
              >
                {addresses.map((address) => (
                  <div
                    key={address.id}
                    className={`border-primary hover:border-primary/80 hover:bg-primary/5 group min-h-[100px] w-full cursor-pointer rounded-[16px] border p-4 transition-colors duration-200 ${
                      selectedAddressId === address.id.toString()
                        ? "border-primary bg-primary/5"
                        : ""
                    }`}
                    onClick={() => handleAddressSelect(address.id.toString())}
                  >
                    <RadioGroupItem
                      value={address.id.toString()}
                      id={`address-${address.id}`}
                    />
                    <Label
                      htmlFor={`address-${address.id}`}
                      className="flex-1 cursor-pointer"
                    >
                      <div className="space-y-1">
                        <div className="font-medium text-black">
                          {address.recipient_name}
                        </div>
                        <div className="text-sm text-gray-600">
                          {address.full_address}
                        </div>
                        {address.delivery_instructions && (
                          <div className="text-xs text-gray-500">
                            หมายเหตุ: {address.delivery_instructions}
                          </div>
                        )}
                      </div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            )}
          />
        </div>
      )}

      <AddAddressButton onClick={handleOpenAddressForm} />

      <AddressForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSubmit={handleAddAddress}
        isEdit={false}
      />
    </>
  );
};
