import React, { useEffect, useRef } from "react";
import { Controller } from "react-hook-form";
import { Card, CardContent, CardHeader } from "@base/card.tsx";
import { InputField } from "@commons/form/input-field";
import { Dropdown } from "@commons/form/dropdown";
import { useTranslation } from "@hooks/useTranslation.ts";
import { RadioGroup, RadioGroupItem } from "@base/radio-group.tsx";
import { Checkbox } from "@base/checkbox.tsx";
import { Label } from "@base/label.tsx";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/plain.css";
import type { TaxInvoiceProps } from "./types.ts";
import {
  getErrorMessage,
  copyShippingToTax,
  animateCardExpand,
  handleCardClick,
} from "./helpers.ts";

const TaxInvoice: React.FC<TaxInvoiceProps> = ({
  control,
  errors,
  watch,
  setValue,
}) => {
  const { t } = useTranslation();
  const cardContentRef = useRef<HTMLDivElement>(null);

  const watchTaxInvoice = watch("taxInvoice.wantTaxInvoice");
  const watchUseSameAddress = watch("taxInvoice.taxInfo.useSameAddress");
  const watchTaxType = watch("taxInvoice.personalInfo.type");

  useEffect(() => {
    if (watchUseSameAddress) {
      copyShippingToTax(watch, setValue);
    }
  }, [watchUseSameAddress]);

  // Handle animations when tax invoice selection changes
  useEffect(() => {
    if (cardContentRef.current) {
      if (watchTaxInvoice) {
        // Animate in when content appears
        animateCardExpand(cardContentRef.current);
      }
    }
  }, [watchTaxInvoice]);

  return (
    <div>
      <h2 className="mb-4 text-[23px] font-medium text-black">
        {t("taxInvoice.title")}
      </h2>

      <div className="space-y-4">
        <Controller
          name="taxInvoice.wantTaxInvoice"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value ? "yes" : "no"}
              onValueChange={(value) => {
                const wantInvoice = value === "yes";
                field.onChange(wantInvoice);
              }}
            >
              {/* Yes Card */}
              <Card
                className={`cursor-pointer rounded-[16px] py-0 transition-colors hover:shadow-md ${
                  field.value === true
                    ? "border-primary-border-card bg-primary/5"
                    : "border-primary-border-card hover:border-gray-300"
                }`}
                onClick={(e) => handleCardClick(e, field.onChange, true)}
              >
                <CardHeader
                  className={
                    field.value === true
                      ? "border-primary rounded-t-[16px] border pt-3 pb-2"
                      : "cursor-pointer rounded-[16px] py-6 md:shadow-sm"
                  }
                >
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="yes" id="tax-yes" />
                    <Label
                      htmlFor="tax-yes"
                      className="cursor-pointer text-base font-medium"
                    >
                      {t("taxInvoice.wantTaxInvoice")}
                    </Label>
                  </div>
                </CardHeader>
                {field.value === true && (
                  <CardContent ref={cardContentRef} className="pt-0">
                    <div className="space-y-6">
                      {/* Personal Information */}
                      <div>
                        <h4 className="mb-4 font-medium">
                          {t("taxInvoice.personalInfo.title")}
                        </h4>
                        <div className="space-y-4">
                          <Controller
                            name="taxInvoice.personalInfo.type"
                            control={control}
                            render={({ field }) => (
                              <Dropdown
                                id="tax-type"
                                label={t("taxInvoice.personalInfo.type")}
                                placeholder={t(
                                  "taxInvoice.personalInfo.typePlaceholder",
                                )}
                                value={field.value}
                                options={[
                                  {
                                    value: "individual",
                                    label: t(
                                      "taxInvoice.personalInfo.individual",
                                    ),
                                  },
                                  {
                                    value: "company",
                                    label: t("taxInvoice.personalInfo.company"),
                                  },
                                ]}
                                error={getErrorMessage(
                                  errors.taxInvoice?.personalInfo?.type,
                                )}
                                onValueChange={field.onChange}
                              />
                            )}
                          />

                          {watchTaxType === "individual" ? (
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                              <Controller
                                name="taxInvoice.personalInfo.firstName"
                                control={control}
                                render={({ field }) => (
                                  <InputField
                                    id="tax-firstName"
                                    placeholder={t(
                                      "taxInvoice.personalInfo.firstName",
                                    )}
                                    error={getErrorMessage(
                                      errors.taxInvoice?.personalInfo
                                        ?.firstName,
                                    )}
                                    {...field}
                                  />
                                )}
                              />
                              <Controller
                                name="taxInvoice.personalInfo.lastName"
                                control={control}
                                render={({ field }) => (
                                  <InputField
                                    id="tax-lastName"
                                    placeholder={t(
                                      "taxInvoice.personalInfo.lastName",
                                    )}
                                    error={getErrorMessage(
                                      errors.taxInvoice?.personalInfo?.lastName,
                                    )}
                                    {...field}
                                  />
                                )}
                              />
                            </div>
                          ) : watchTaxType === "company" ? (
                            <Controller
                              name="taxInvoice.personalInfo.companyName"
                              control={control}
                              render={({ field }) => (
                                <InputField
                                  id="tax-companyName"
                                  placeholder={t(
                                    "taxInvoice.personalInfo.companyName",
                                  )}
                                  error={getErrorMessage(
                                    errors.taxInvoice?.personalInfo
                                      ?.companyName,
                                  )}
                                  {...field}
                                />
                              )}
                            />
                          ) : null}

                          <Controller
                            name="taxInvoice.personalInfo.email"
                            control={control}
                            render={({ field }) => (
                              <InputField
                                id="tax-email"
                                placeholder={t("taxInvoice.personalInfo.email")}
                                type="email"
                                error={getErrorMessage(
                                  errors.taxInvoice?.personalInfo?.email,
                                )}
                                {...field}
                              />
                            )}
                          />

                          <div>
                            <Label className="mb-2 block">
                              {t("taxInvoice.personalInfo.phone")}
                            </Label>
                            <Controller
                              name="taxInvoice.personalInfo.phone"
                              control={control}
                              render={({ field }) => (
                                <PhoneInput
                                  country="th"
                                  value={field.value}
                                  onChange={field.onChange}
                                  containerClass="w-full"
                                  inputClass="w-full"
                                />
                              )}
                            />
                            {errors.taxInvoice?.personalInfo?.phone && (
                              <p className="mt-1 text-xs text-red-600">
                                {errors.taxInvoice.personalInfo.phone.message}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Tax Information */}
                      <div>
                        <h4 className="mb-4 font-medium">
                          {t("taxInvoice.taxInfo.title")}
                        </h4>
                        <div className="space-y-4">
                          <Controller
                            name="taxInvoice.taxInfo.taxId"
                            control={control}
                            render={({ field }) => (
                              <InputField
                                id="tax-id"
                                placeholder={
                                  watchTaxType === "individual"
                                    ? t("taxInvoice.taxInfo.taxIdIndividual")
                                    : watchTaxType === "company"
                                      ? t("taxInvoice.taxInfo.taxIdCompany")
                                      : t("taxInvoice.taxInfo.taxId")
                                }
                                error={getErrorMessage(
                                  errors.taxInvoice?.taxInfo?.taxId,
                                )}
                                {...field}
                              />
                            )}
                          />

                          <Controller
                            name="taxInvoice.taxInfo.useSameAddress"
                            control={control}
                            render={({ field }) => (
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="same-address"
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                                <Label htmlFor="same-address">
                                  {t("taxInvoice.taxInfo.useSameAddress")}
                                </Label>
                              </div>
                            )}
                          />

                          {!watchUseSameAddress && (
                            <>
                              <Controller
                                name="taxInvoice.taxInfo.address_line1"
                                control={control}
                                render={({ field }) => (
                                  <InputField
                                    id="tax-address-line1"
                                    placeholder={t(
                                      "taxInvoice.taxInfo.addressLine1",
                                    )}
                                    error={getErrorMessage(
                                      errors.taxInvoice?.taxInfo?.address_line1,
                                    )}
                                    {...field}
                                  />
                                )}
                              />

                              <Controller
                                name="taxInvoice.taxInfo.address_line2"
                                control={control}
                                render={({ field }) => (
                                  <InputField
                                    id="tax-address-line2"
                                    placeholder={t(
                                      "taxInvoice.taxInfo.addressLine2",
                                    )}
                                    {...field}
                                  />
                                )}
                              />

                              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <Controller
                                  name="taxInvoice.taxInfo.subdistrict"
                                  control={control}
                                  render={({ field }) => (
                                    <InputField
                                      id="tax-subdistrict"
                                      placeholder={t(
                                        "taxInvoice.taxInfo.subdistrict",
                                      )}
                                      error={getErrorMessage(
                                        errors.taxInvoice?.taxInfo?.subdistrict,
                                      )}
                                      {...field}
                                    />
                                  )}
                                />
                                <Controller
                                  name="taxInvoice.taxInfo.district"
                                  control={control}
                                  render={({ field }) => (
                                    <InputField
                                      id="tax-district"
                                      placeholder={t(
                                        "taxInvoice.taxInfo.district",
                                      )}
                                      error={getErrorMessage(
                                        errors.taxInvoice?.taxInfo?.district,
                                      )}
                                      {...field}
                                    />
                                  )}
                                />
                              </div>

                              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <Controller
                                  name="taxInvoice.taxInfo.city"
                                  control={control}
                                  render={({ field }) => (
                                    <InputField
                                      id="tax-city"
                                      placeholder={t("taxInvoice.taxInfo.city")}
                                      error={getErrorMessage(
                                        errors.taxInvoice?.taxInfo?.city,
                                      )}
                                      {...field}
                                    />
                                  )}
                                />
                                <Controller
                                  name="taxInvoice.taxInfo.province"
                                  control={control}
                                  render={({ field }) => (
                                    <InputField
                                      id="tax-province"
                                      placeholder={t(
                                        "taxInvoice.taxInfo.province",
                                      )}
                                      error={getErrorMessage(
                                        errors.taxInvoice?.taxInfo?.province,
                                      )}
                                      {...field}
                                    />
                                  )}
                                />
                                <Controller
                                  name="taxInvoice.taxInfo.country"
                                  control={control}
                                  render={({ field }) => (
                                    <InputField
                                      id="tax-country"
                                      placeholder={t(
                                        "taxInvoice.taxInfo.country",
                                      )}
                                      error={getErrorMessage(
                                        errors.taxInvoice?.taxInfo?.country,
                                      )}
                                      {...field}
                                    />
                                  )}
                                />
                                <Controller
                                  name="taxInvoice.taxInfo.postal_code"
                                  control={control}
                                  render={({ field }) => (
                                    <InputField
                                      id="tax-postal-code"
                                      placeholder={t(
                                        "taxInvoice.taxInfo.postalCode",
                                      )}
                                      error={getErrorMessage(
                                        errors.taxInvoice?.taxInfo?.postal_code,
                                      )}
                                      {...field}
                                    />
                                  )}
                                />
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* No Card */}
              <Card
                className={`cursor-pointer rounded-[16px] transition-colors hover:shadow-md ${
                  field.value === false
                    ? "border-primary bg-primary/5"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={(e) => handleCardClick(e, field.onChange, false)}
              >
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="no" id="tax-no" />
                    <Label
                      htmlFor="tax-no"
                      className="cursor-pointer text-base font-medium"
                    >
                      {t("taxInvoice.noTaxInvoice")}
                    </Label>
                  </div>
                </CardHeader>
              </Card>
            </RadioGroup>
          )}
        />
      </div>
    </div>
  );
};

export default TaxInvoice;