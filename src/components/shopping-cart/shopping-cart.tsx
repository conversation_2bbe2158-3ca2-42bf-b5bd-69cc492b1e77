import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@base/card";

import { useCartItems } from "@services/api/auth/cart/cart-service";
import { Spinner } from "@/components/commons/loading-spinner";
import type { CartItem } from "@/pages/checkout/types";
import {cn} from "@lib/utils.ts";

interface ShoppingCartProps {
  className?: string;
  showTitle?: boolean;
}

const ShoppingCart: React.FC<ShoppingCartProps> = ({ className = "" }) => {
  const { data: cartsResponse, isLoading, isError } = useCartItems();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center p-6">
          <Spinner size="medium" />
        </CardContent>
      </Card>
    );
  }

  if (isError || !cartsResponse?.results) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">
            ไม่สามารถโหลดข้อมูลตะกร้าได้
          </p>
        </CardContent>
      </Card>
    );
  }

  const cartItems: CartItem[] = cartsResponse.results;
  const subtotal = cartItems.reduce(
    (sum, item) => sum + item.product_variant.price * item.quantity,
    0,
  );
  const shipping = 50; // Default shipping cost
  const discount = 0; // Will be calculated based on discount code
  const total = subtotal + shipping - discount;

  return (
    <Card className={cn(className, "bg-background-card border-none shadow-lg")}>
      <CardHeader className="lg:hidden">
        <CardTitle>สรุปคำสั่งซื้อ</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cart Items */}
        <div className="space-y-3">
          {cartItems.map((item) => (
            <div key={item.id} className="flex gap-3">
              <div className="relative">
                <img
                  src={item.product_variant.first_image_url}
                  alt={item.product_variant.product_name}
                  className="h-16 w-16 rounded-md object-cover"
                />
                <span className="absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center rounded-full bg-gray-500 text-xs text-white">
                  {item.quantity}
                </span>
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="text-primary-dark truncate text-[16px] font-semibold">
                  {item.product_variant.product_name}
                </h3>
                {item.product_variant.sku.length > 0 && (
                  <a className="text-normal rounded-lg border-1 border-[#E3CAA5] px-2 py-1 text-[12px]">
                    {item.product_variant.sku}
                  </a>
                )}
              </div>
              <h3 className="text-[16px] font-bold">
                ฿{(item.product_variant.price * item.quantity).toLocaleString()}
              </h3>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="space-y-2 pt-4">
          <div className="flex justify-between text-sm">
            <span>Subtotal</span>
            <span>฿{subtotal.toLocaleString()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Shipping</span>
            <span>฿{shipping.toLocaleString()}</span>
          </div>
          {discount > 0 && (
            <div className="flex justify-between text-sm text-green-600">
              <span>Discount</span>
              <span>-฿{discount.toLocaleString()}</span>
            </div>
          )}
          <div className="border-t border-black pt-2">
            <div className="flex justify-between font-semibold">
              <span>Total</span>
              <span>฿{total.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export { ShoppingCart };
export default ShoppingCart;
